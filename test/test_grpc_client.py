#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试gRPC客户端
"""

import sys, os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ufn_protocol.grpc_client import GRPCClient

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_grpc_client():
    """测试gRPC客户端"""
    client = GRPCClient('localhost:50051')
    
    # 测试查询升级信息
    logger.info("Testing query_upgrade_info...")
    result = client.query_upgrade_info('340202420300715', b'NTB2GV31')
    logger.info(f"Result: {result}")
    
    if result:
        # 文件数据现在直接在 query_upgrade_info 的响应中
        if 'file_data' in result and result['file_data']:
            logger.info(f"File data received directly: {len(result['file_data'])} bytes")
        else:
            logger.warning("No file data in response")
    
    return result is not None

if __name__ == '__main__':
    success = test_grpc_client()
    sys.exit(0 if success else 1)
