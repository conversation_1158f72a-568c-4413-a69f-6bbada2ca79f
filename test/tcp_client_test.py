#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCP客户端测试用例
用于测试TCP服务端的各种功能
"""

import socket
import time
import threading
import json
import sys
from typing import Optional

class TCPClientTester:
    def __init__(self, host: str = 'localhost', port: int = 8000, timeout: int = 10):
        """
        初始化TCP客户端测试器
        
        Args:
            host: 服务器地址
            port: 服务器端口
            timeout: 连接超时时间
        """
        self.host = host
        self.port = port
        self.timeout = timeout
        self.client_socket: Optional[socket.socket] = None
        self.connected = False
        
    def connect(self) -> bool:
        """
        连接到服务器
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.client_socket.settimeout(self.timeout)
            self.client_socket.connect((self.host, self.port))
            self.connected = True
            print(f"✓ 成功连接到服务器 {self.host}:{self.port}")
            return True
        except socket.timeout:
            print(f"✗ 连接超时: {self.host}:{self.port}")
            return False
        except ConnectionRefusedError:
            print(f"✗ 连接被拒绝: {self.host}:{self.port}")
            return False
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.client_socket and self.connected:
            try:
                self.client_socket.close()
                self.connected = False
                print("✓ 已断开连接")
            except Exception as e:
                print(f"断开连接时出错: {e}")
    
    def send_data(self, data: str, encoding: str = 'utf-8') -> bool:
        """
        发送数据到服务器
        
        Args:
            data: 要发送的数据
            encoding: 编码格式
            
        Returns:
            bool: 发送是否成功
        """
        if not self.connected or not self.client_socket:
            print("✗ 未连接到服务器")
            return False
        
        try:
            # 发送数据
            self.client_socket.send(data.encode(encoding))
            print(f"→ 发送数据: {data[:50]}{'...' if len(data) > 50 else ''}")
            return True
        except Exception as e:
            print(f"✗ 发送数据失败: {e}")
            return False
    
    def receive_data(self, buffer_size: int = 1024, encoding: str = 'utf-8') -> Optional[str]:
        """
        接收服务器数据
        
        Args:
            buffer_size: 接收缓冲区大小
            encoding: 编码格式
            
        Returns:
            Optional[str]: 接收到的数据
        """
        if not self.connected or not self.client_socket:
            print("✗ 未连接到服务器")
            return None
        
        try:
            data = self.client_socket.recv(buffer_size)
            if data:
                decoded_data = data.decode(encoding)
                print(f"← 接收数据: {decoded_data[:50]}{'...' if len(decoded_data) > 50 else ''}")
                return decoded_data
            else:
                print("← 接收到空数据，服务器可能已关闭连接")
                return None
        except socket.timeout:
            print("✗ 接收数据超时")
            return None
        except Exception as e:
            print(f"✗ 接收数据失败: {e}")
            return None
    
    def send_and_receive(self, data: str, buffer_size: int = 1024) -> Optional[str]:
        """
        发送数据并接收响应
        
        Args:
            data: 要发送的数据
            buffer_size: 接收缓冲区大小
            
        Returns:
            Optional[str]: 服务器响应
        """
        if self.send_data(data):
            return self.receive_data(buffer_size)
        return None

def test_basic_connection(host: str = 'localhost', port: int = 8888):
    """基本连接测试"""
    print("\n=== 基本连接测试 ===")
    tester = TCPClientTester(host, port)
    
    # 测试连接
    if tester.connect():
        time.sleep(1)  # 等待连接稳定
        tester.disconnect()
        return True
    return False

def test_echo_functionality(host: str = 'localhost', port: int = 8888):
    """回显功能测试"""
    print("\n=== 回显功能测试 ===")
    tester = TCPClientTester(host, port)
    
    if not tester.connect():
        return False
    
    # 测试不同长度的消息
    test_messages = [
        "Hello",
        "Hello, World!",
        "这是一个中文测试消息",
        "A" * 100,  # 长消息测试
        json.dumps({"type": "test", "data": "JSON格式测试"})
    ]
    
    success_count = 0
    for msg in test_messages:
        print(f"\n测试消息: {msg[:30]}{'...' if len(msg) > 30 else ''}")
        response = tester.send_and_receive(msg)
        if response and response.strip() == msg.strip():
            print("✓ 回显测试通过")
            success_count += 1
        else:
            print(f"✗ 回显测试失败，期望: {msg}, 实际: {response}")
    
    tester.disconnect()
    print(f"\n回显测试结果: {success_count}/{len(test_messages)} 通过")
    return success_count == len(test_messages)

def test_multiple_messages(host: str = 'localhost', port: int = 8888):
    """多条消息测试"""
    print("\n=== 多条消息测试 ===")
    tester = TCPClientTester(host, port)
    
    if not tester.connect():
        return False
    
    # 连续发送多条消息
    for i in range(5):
        msg = f"Message {i+1}"
        response = tester.send_and_receive(msg)
        if response:
            print(f"✓ 消息 {i+1} 处理成功")
        else:
            print(f"✗ 消息 {i+1} 处理失败")
        time.sleep(0.1)  # 短暂延迟
    
    tester.disconnect()
    return True

def test_concurrent_connections(host: str = 'localhost', port: int = 8888, num_clients: int = 3):
    """并发连接测试"""
    print(f"\n=== 并发连接测试 ({num_clients}个客户端) ===")
    
    results = []
    
    def client_worker(client_id: int):
        """客户端工作线程"""
        tester = TCPClientTester(host, port)
        success = False
        
        if tester.connect():
            msg = f"Client {client_id} message"
            response = tester.send_and_receive(msg)
            if response:
                print(f"✓ 客户端 {client_id} 通信成功")
                success = True
            else:
                print(f"✗ 客户端 {client_id} 通信失败")
            tester.disconnect()
        else:
            print(f"✗ 客户端 {client_id} 连接失败")
        
        results.append(success)
    
    # 创建并启动多个客户端线程
    threads = []
    for i in range(num_clients):
        thread = threading.Thread(target=client_worker, args=(i+1,))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    success_count = sum(results)
    print(f"\n并发测试结果: {success_count}/{num_clients} 个客户端成功")
    return success_count == num_clients

def test_long_connection(host: str = 'localhost', port: int = 8888, duration: int = 10):
    """长连接测试"""
    print(f"\n=== 长连接测试 ({duration}秒) ===")
    tester = TCPClientTester(host, port)
    
    if not tester.connect():
        return False
    
    start_time = time.time()
    message_count = 0
    
    try:
        while time.time() - start_time < duration:
            msg = f"Keep-alive message {message_count + 1}"
            response = tester.send_and_receive(msg)
            if response:
                message_count += 1
                print(f"✓ 发送第 {message_count} 条保持连接消息")
            else:
                print("✗ 长连接中断")
                break
            time.sleep(2)  # 每2秒发送一次
    except KeyboardInterrupt:
        print("\n用户中断测试")
    
    tester.disconnect()
    print(f"长连接测试完成，共发送 {message_count} 条消息")
    return True

def test_error_handling(host: str = 'localhost', port: int = 8888):
    """错误处理测试"""
    print("\n=== 错误处理测试 ===")
    tester = TCPClientTester(host, port)
    
    if not tester.connect():
        return False
    
    # 测试异常数据
    test_cases = [
        ("空消息", ""),
        ("特殊字符", "!@#$%^&*()_+{}[]|\\:;\"'<>?,./"),
        ("超长消息", "X" * 10000),
        ("Unicode字符", "🌟🚀💻🎉"),
        ("换行符", "Line1\nLine2\nLine3"),
    ]
    
    for test_name, test_data in test_cases:
        print(f"\n测试 {test_name}:")
        response = tester.send_and_receive(test_data)
        if response is not None:
            print(f"✓ 服务器正常处理了{test_name}")
        else:
            print(f"✗ 服务器处理{test_name}时出现问题")
    
    tester.disconnect()
    return True

def main():
    """主测试函数"""
    print("TCP客户端测试套件")
    print("=" * 50)
    
    # 可以通过命令行参数指定服务器地址和端口
    host = sys.argv[1] if len(sys.argv) > 1 else 'localhost'
    port = int(sys.argv[2]) if len(sys.argv) > 2 else 8888
    
    print(f"目标服务器: {host}:{port}")
    
    # 运行所有测试
    tests = [
        ("基本连接测试", lambda: test_basic_connection(host, port)),
        ("回显功能测试", lambda: test_echo_functionality(host, port)),
        ("多条消息测试", lambda: test_multiple_messages(host, port)),
        ("并发连接测试", lambda: test_concurrent_connections(host, port, 3)),
        ("长连接测试", lambda: test_long_connection(host, port, 10)),
        ("错误处理测试", lambda: test_error_handling(host, port)),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 出现异常: {e}")
        
        time.sleep(1)  # 测试间隔
    
    # 总结
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 个测试通过")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！服务器功能正常")
        return 0
    else:
        print("⚠️  部分测试失败，请检查服务器实现")
        return 1

if __name__ == "__main__":
    sys.exit(main())