#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整OTA测试脚本
启动gRPC服务器、UFN服务器，并测试完整的升级流程
"""

import os
import sys
import time
import logging
import threading
import subprocess
from typing import Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def start_grpc_server():
    """启动gRPC服务器"""
    logger.info("Starting gRPC server...")
    try:
        # 导入并启动gRPC服务器
        from test.grpc_server import serve
        serve(port=50051)
    except Exception as e:
        logger.error(f"Failed to start gRPC server: {e}")


def start_ufn_server():
    """启动UFN服务器"""
    logger.info("Starting UFN server...")
    try:
        from ufn_protocol import UFNServer
        from ufn_protocol.grpc_client import GRPCClient
        
        # 创建gRPC客户端
        grpc_client = GRPCClient('localhost:50051')
        
        # 创建UFN服务器
        server = UFNServer(
            host='0.0.0.0', 
            port=8080, 
            grpc_server_url='localhost:50051'
        )
        
        logger.info("UFN server starting on port 8080...")
        server.start()
        
    except Exception as e:
        logger.error(f"Failed to start UFN server: {e}")


def run_device_test():
    """运行设备测试"""
    logger.info("Waiting for servers to start...")
    time.sleep(3)  # 等待服务器启动
    
    try:
        from test_device_messages import test_ota_flow
        return test_ota_flow()
    except Exception as e:
        logger.error(f"Device test failed: {e}")
        return False


def check_test_file():
    """检查测试文件是否存在"""
    test_file = "Resource/4c4e96cf-9937-4299-b113-5948bb7f2e53_ccu-10045_NT-B2G-V3 2024.7.10_Ex.yxp"
    if os.path.exists(test_file):
        file_size = os.path.getsize(test_file)
        logger.info(f"Test file found: {test_file} ({file_size} bytes)")
        return True
    else:
        logger.warning(f"Test file not found: {test_file}")
        logger.info("Will use mock data for testing")
        return False


def main():
    """主函数"""
    logger.info("Complete OTA Test Suite")
    logger.info("=" * 50)
    
    # 检查测试文件
    check_test_file()
    
    # 启动gRPC服务器线程
    grpc_thread = threading.Thread(target=start_grpc_server, daemon=True)
    grpc_thread.start()
    
    # 等待gRPC服务器启动
    time.sleep(2)
    
    # 启动UFN服务器线程
    ufn_thread = threading.Thread(target=start_ufn_server, daemon=True)
    ufn_thread.start()
    
    # 等待UFN服务器启动
    time.sleep(3)
    
    try:
        # 运行设备测试
        logger.info("\n" + "=" * 50)
        logger.info("Running device message tests...")
        logger.info("=" * 50)
        
        success = run_device_test()
        
        if success:
            logger.info("\n" + "=" * 50)
            logger.info("🎉 All tests completed successfully!")
            logger.info("The OTA upgrade flow is working correctly.")
            logger.info("=" * 50)
        else:
            logger.error("\n" + "=" * 50)
            logger.error("❌ Some tests failed!")
            logger.error("Please check the logs for details.")
            logger.error("=" * 50)
        
        # 保持服务器运行一段时间以便观察
        logger.info("\nServers will continue running for 30 seconds...")
        logger.info("You can manually test with other clients during this time.")
        time.sleep(30)
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        logger.info("\nTest interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"Test suite failed: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
