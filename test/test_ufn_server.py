#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UFN服务器
"""

import sys
import os
import logging
import threading
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ufn_protocol.server import UFNServer



logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_fn_server.log'),  # 输出到文件
        logging.StreamHandler()  # 同时输出到控制台
    ]
)
logger = logging.getLogger(__name__)

def test_ufn_server():
    """测试UFN服务器"""
    try:
        # 创建UFN服务器，连接到gRPC服务器
        server = UFNServer(
            host='0.0.0.0', 
            port=8080, 
            grpc_server_url='localhost:50051'
        )
        
        logger.info("UFN服务器正在启动...")
        server.start()
        
    except Exception as e:
        logger.error(f"UFN服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_ufn_server()
