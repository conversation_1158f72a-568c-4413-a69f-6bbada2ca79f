#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查设备消息的校验和
"""

import struct

def calculate_checksum(data: bytes) -> int:
    """计算检验和（前面所有数据和）"""
    return sum(data) & 0xFFFFFFFF

def check_version_message():
    """检查版本请求消息"""
    hex_data = "00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 b0 13 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00"
    data = bytes.fromhex(hex_data.replace(' ', ''))
    
    print(f"消息长度: {len(data)} 字节")
    print(f"消息内容: {data.hex()}")
    
    # 解析消息
    length = struct.unpack('>H', data[0:2])[0]
    func_code = struct.unpack('>H', data[2:4])[0]
    print(f"报文长度: {length}")
    print(f"功能代码: 0x{func_code:04X}")
    
    # 检查校验和
    checksum_data = data[46:50]
    received_checksum = struct.unpack('>I', checksum_data)[0]
    calculated_checksum = calculate_checksum(data[0:46])
    
    print(f"接收到的校验和: {received_checksum} (0x{received_checksum:08X})")
    print(f"计算的校验和: {calculated_checksum} (0x{calculated_checksum:08X})")
    print(f"校验和匹配: {received_checksum == calculated_checksum}")
    
    # 修正校验和
    if received_checksum != calculated_checksum:
        print("\n修正后的消息:")
        corrected_data = bytearray(data)
        struct.pack_into('>I', corrected_data, 46, calculated_checksum)
        print(f"修正后的消息: {corrected_data.hex()}")
        
        # 格式化输出
        hex_str = corrected_data.hex()
        formatted = ' '.join(hex_str[i:i+2] for i in range(0, len(hex_str), 2))
        print(f"格式化: {formatted}")

def check_program_info_message():
    """检查程序信息请求消息"""
    hex_data = "00 08 84 03 8f 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00"
    data = bytes.fromhex(hex_data.replace(' ', ''))
    
    print(f"\n程序信息请求消息:")
    print(f"消息长度: {len(data)} 字节")
    print(f"消息内容: {data.hex()}")
    
    # 解析消息
    length = struct.unpack('>H', data[0:2])[0]
    func_code = struct.unpack('>H', data[2:4])[0]
    print(f"报文长度: {length}")
    print(f"功能代码: 0x{func_code:04X}")
    
    # 检查校验和
    checksum_data = data[4:8]
    received_checksum = struct.unpack('>I', checksum_data)[0]
    calculated_checksum = calculate_checksum(data[0:4])
    
    print(f"接收到的校验和: {received_checksum} (0x{received_checksum:08X})")
    print(f"计算的校验和: {calculated_checksum} (0x{calculated_checksum:08X})")
    print(f"校验和匹配: {received_checksum == calculated_checksum}")
    
    # 修正校验和
    if received_checksum != calculated_checksum:
        print("\n修正后的消息:")
        corrected_data = bytearray(data)
        struct.pack_into('>I', corrected_data, 4, calculated_checksum)
        print(f"修正后的消息: {corrected_data.hex()}")
        
        # 格式化输出
        hex_str = corrected_data.hex()
        formatted = ' '.join(hex_str[i:i+2] for i in range(0, len(hex_str), 2))
        print(f"格式化: {formatted}")

if __name__ == '__main__':
    check_version_message()
    check_program_info_message()
