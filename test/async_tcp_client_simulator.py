import asyncio
import random
import string
from datetime import datetime

HOST = '127.0.0.1'  # 如果服务器在远程，请修改为 IP
PORT = 12345
DEVICE_COUNT = 200  # 模拟设备数量
MESSAGES_PER_DEVICE = 5  # 每台设备发送的消息数
DELAY_BETWEEN_MESSAGES = 1  # 每条消息之间的延迟（秒）

def generate_device_id():
    return 'DEV-' + ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))

async def simulate_device(device_id):
    try:
        reader, writer = await asyncio.open_connection(HOST, PORT)
        print(f"[连接] {device_id} 已连接服务器")

        for i in range(MESSAGES_PER_DEVICE):
            msg = f"device_id:{device_id} msg_{i} at {datetime.now()}"
            writer.write(msg.encode())
            await writer.drain()

            # 等待服务器响应（ACK）
            try:
                data = await asyncio.wait_for(reader.readline(), timeout=5)
                print(f"[响应] {device_id} 收到: {data.decode().strip()}")
            except asyncio.TimeoutError:
                print(f"[超时] {device_id} 等待ACK超时")

            await asyncio.sleep(DELAY_BETWEEN_MESSAGES)

        writer.close()
        await writer.wait_closed()
        print(f"[断开] {device_id} 关闭连接")

    except Exception as e:
        print(f"[异常] {device_id}: {e}")

async def main():
    tasks = []
    for _ in range(DEVICE_COUNT):
        device_id = generate_device_id()
        tasks.append(simulate_device(device_id))

    await asyncio.gather(*tasks)

if __name__ == "__main__":
    asyncio.run(main())