# UFN Protocol Server

UFN (Upgrade From NET) 协议服务端框架，用于远程设备固件升级。

## 项目简介

UFN Protocol Server 是一个基于 TCP 的固件升级服务器，支持设备版本查询、程序信息获取、分块数据下载等功能。该项目采用模块化设计，易于扩展和维护。

## 功能特性

- **版本管理**: 支持多程序版本管理和版本比较
- **分块传输**: 支持大文件分块下载，提高传输效率
- **并发处理**: 多线程处理客户端连接，支持并发访问
- **协议完整**: 完整实现 UFN 协议规范
- **模块化设计**: 清晰的代码结构，易于维护和扩展

## 协议支持

### 功能代码

| 功能 | 请求码 | 响应码 | 描述 |
|------|--------|--------|------|
| 获取版本信息 | 0x8301 | 0x8302 | 查询服务器程序版本 |
| 获取程序信息 | 0x8403 | 0x8404 | 获取程序详细信息 |
| 下载程序数据 | 0x9101 | 0x9102 | 分块下载程序数据 |
| 发送启动信息 | 0x8303 | 0x8303 | 设备启动信息上报 |

### 升级控制

| 控制码 | 值 | 描述 |
|--------|-----|------|
| FORBIDDEN | 0xA0 | 服务器禁止此设备升级 |
| VERSION_CHECK | 0xA1 | 设备发现版本更新了就升级 |
| FORCE_UPGRADE | 0xA2 | 立即升级，判断版本一致才下载 |
| FORCE_REPLACE | 0xA3 | 立即升级，不判断版本 |
| NO_SUITABLE | 0xA4 | 服务器没有适合该版本程序 |

## 项目结构

```
├── main.py                     # 程序入口
├── ufn_protocol/              # UFN协议包
│   ├── __init__.py            # 包初始化
│   ├── constants.py           # 协议常量定义
│   ├── message_parser.py      # 消息解析器
│   ├── message_builder.py     # 消息构建器
│   ├── program_manager.py     # 程序数据管理器
│   ├── client_handler.py      # 客户端连接处理器
│   └── server.py             # UFN服务器主类
├── Resource/                  # 参考资源文件
└── README.md                 # 项目说明
```

## 快速开始

### 环境要求

- Python 3.7+
- 无额外依赖包

### 安装运行

1. 克隆项目
```bash
git clone <repository-url>
cd ufn-protocol-server
```

2. 运行服务器
```bash
python main.py
```

### 基本使用

```python
from ufn_protocol import UFNServer

# 创建服务器实例
server = UFNServer(host='0.0.0.0', port=8080)

# 添加程序数据
server.add_program(
    program_id='1234567890abcdef',
    version=10100,  # 版本号 1.01.00
    data=firmware_data
)

# 从文件加载程序
server.load_program_from_file(
    program_id='abcdef1234567890',
    file_path='firmware.bin',
    version=10200
)

# 启动服务器
try:
    server.start()
except KeyboardInterrupt:
    server.stop()
```

## API 文档

### UFNServer

主服务器类，负责管理整个 UFN 服务。

#### 构造函数

```python
UFNServer(host='0.0.0.0', port=8080, timeout=10)
```

**参数:**
- `host`: 服务器绑定地址
- `port`: 服务器端口
- `timeout`: 客户端连接超时时间（秒）

#### 主要方法

##### add_program()

添加程序数据到服务器。

```python
add_program(program_id, version, data, area=0, option=0, mark1=0, mark2=0)
```

**参数:**
- `program_id`: 程序ID（16位十六进制字符串）
- `version`: 程序版本号
- `data`: 程序二进制数据
- `area`, `option`, `mark1`, `mark2`: 程序标识参数

##### load_program_from_file()

从文件加载程序数据。

```python
load_program_from_file(program_id, file_path, version, area=0, option=0, mark1=0, mark2=0)
```

**返回:** `bool` - 加载是否成功

##### start()

启动服务器，开始监听客户端连接。

##### stop()

停止服务器。

### ProgramManager

程序数据管理器，负责程序数据的存储和管理。

#### 主要方法

- `add_program()`: 添加程序数据
- `load_program_from_file()`: 从文件加载程序
- `get_program_data()`: 获取所有程序数据
- `has_program()`: 检查程序是否存在
- `get_program()`: 获取指定程序数据

### MessageParser

消息解析器，负责解析客户端发送的协议消息。

#### 静态方法

- `parse_get_version_request()`: 解析版本查询请求
- `parse_get_program_info_request()`: 解析程序信息请求
- `parse_download_request()`: 解析下载请求
- `parse_startup_info()`: 解析启动信息
- `calculate_checksum()`: 计算校验和

### MessageBuilder

消息构建器，负责构建发送给客户端的响应消息。

#### 静态方法

- `create_get_version_response()`: 创建版本查询响应
- `create_get_program_info_response()`: 创建程序信息响应
- `create_download_response()`: 创建下载数据响应

## 协议详解

### 消息格式

所有消息都采用以下基本格式：

```
+--------+--------+--------+--------+
| Length | FuncCode |    Data...     |
+--------+--------+--------+--------+
|   2B   |    2B    |    Variable    |
+--------+--------+--------+--------+
```

- **Length**: 消息总长度（小端序）
- **FuncCode**: 功能代码（大端序）
- **Data**: 消息数据内容
- **Checksum**: 校验和（消息末尾4字节，大端序）

### 版本查询流程

1. 客户端发送版本查询请求 (0x8301)
2. 服务器解析请求，比较版本
3. 服务器返回版本响应 (0x8302) 和升级控制指令
4. 客户端根据升级控制决定后续操作

### 程序下载流程

1. 客户端请求程序信息 (0x8403)
2. 服务器返回程序信息 (0x8404)
3. 客户端分块请求程序数据 (0x9101)
4. 服务器返回对应数据块 (0x9102)
5. 重复步骤3-4直到下载完成
6. 客户端发送启动信息 (0x8303)

## 配置说明

### 日志配置

项目使用 Python 标准 logging 模块，默认配置：

```python
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
```

可以通过修改 `main.py` 中的配置来调整日志级别和格式。

### 服务器配置

- **默认端口**: 8080
- **默认超时**: 10秒
- **默认分块大小**: 1024字节

## 扩展开发

### 添加新的协议功能

1. 在 `constants.py` 中添加新的功能代码
2. 在 `message_parser.py` 中添加解析方法
3. 在 `message_builder.py` 中添加构建方法
4. 在 `client_handler.py` 中添加处理逻辑

### 自定义程序管理

继承 `ProgramManager` 类，实现自定义的程序数据管理逻辑：

```python
class CustomProgramManager(ProgramManager):
    def load_from_database(self):
        # 从数据库加载程序数据
        pass
    
    def save_to_database(self):
        # 保存程序数据到数据库
        pass
```

## 故障排除

### 常见问题

1. **连接超时**
   - 检查防火墙设置
   - 确认端口未被占用
   - 调整超时时间参数

2. **校验和错误**
   - 检查数据传输完整性
   - 确认字节序设置正确

3. **程序加载失败**
   - 检查文件路径和权限
   - 确认文件格式正确

### 调试模式

设置日志级别为 DEBUG 以获取详细信息：

```python
logging.basicConfig(level=logging.DEBUG)
```

