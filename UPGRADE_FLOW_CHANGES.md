# 升级流程修改说明

## 修改概述

根据需求，现在 `query_upgrade_info` 方法直接返回文件字节数组，而不是文件地址，因此不再需要二次请求文件内容。

## 主要修改

### 1. `ufn_protocol/grpc_client.py`

- **修改 `query_upgrade_info` 方法文档**：更新返回值说明，明确包含 `file_data` 字段
- **标记 `download_file_content` 方法为已弃用**：添加 `@deprecated` 注释，说明现在不再需要二次请求

### 2. `ufn_protocol/program_manager.py`

- **修改 `load_program_from_grpc` 方法签名**：
  - 第二个参数从 `file_url: str` 改为 `file_data: bytes`
  - 添加可选的 `file_url: str = None` 参数（仅用于日志记录）
- **移除 gRPC 下载逻辑**：不再调用 `self.grpc_client.download_file_content()`
- **直接使用文件数据**：使用传入的 `file_data` 参数

### 3. `ufn_protocol/client_handler.py`

- **修改 `_handle_get_version` 方法**：
  - 在 `response_data` 中添加 `program_id` 字段
  - 在 `response_data` 中添加 `file_data` 字段
- **修改 `_handle_get_program_info` 方法**：
  - 调用 `load_program_from_grpc` 时使用 `file_data` 而不是 `file_url`
- **修改 `_handle_download_data` 调用**：传递 `upgrade_context` 参数

### 4. `test/test_grpc_client.py`

- **移除二次下载测试**：不再测试 `download_file_content` 方法
- **添加文件数据验证**：检查 `query_upgrade_info` 响应中的 `file_data` 字段

### 5. `test/grpc_server.py`

- **修改 `QueryUpgradeInfo` 方法**：
  - 直接在响应中加载并返回文件数据
  - 使用文件缓存机制提高性能
  - 修复字段名：使用 `program_type` 而不是 `program_id`

## 流程对比

### 修改前的流程
```
1. 调用 query_upgrade_info() -> 返回 file_url
2. 调用 download_file_content(file_url) -> 返回 file_data
3. 使用 file_data 加载程序
```

### 修改后的流程
```
1. 调用 query_upgrade_info() -> 直接返回 file_data
2. 使用 file_data 加载程序
```

## 优势

1. **减少网络请求**：从两次请求减少到一次请求
2. **提高性能**：减少网络延迟和带宽使用
3. **简化逻辑**：移除了二次请求的复杂性
4. **保持兼容性**：仍然返回 `file_url` 字段用于向后兼容

## 测试

创建了新的测试脚本 `test_new_flow.py` 来验证修改后的流程：

```bash
python test_new_flow.py
```

## 注意事项

1. `download_file_content` 方法仍然保留，但标记为已弃用
2. `file_url` 字段仍然在响应中返回，用于向后兼容
3. 所有现有的 API 接口保持不变，只是内部实现逻辑改变
4. 需要确保 gRPC 服务器端也相应更新，在 `QueryUpgradeInfo` 响应中包含文件数据

## 向后兼容性

- 现有代码仍然可以工作，因为所有字段都保留
- 如果有代码仍在使用 `download_file_content`，建议逐步迁移到新流程
- 新代码应该直接使用 `query_upgrade_info` 返回的 `file_data` 字段
