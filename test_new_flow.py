#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的升级流程（直接返回文件数据，无需二次请求）
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ufn_protocol.grpc_client import GRPCClient
from ufn_protocol.program_manager import ProgramManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_new_upgrade_flow():
    """测试新的升级流程"""
    logger.info("=== 测试新的升级流程 ===")
    
    # 1. 创建 gRPC 客户端
    grpc_client = GRPCClient('localhost:50051')
    
    # 2. 查询升级信息（现在直接返回文件数据）
    logger.info("1. 查询升级信息...")
    upgrade_info = grpc_client.query_upgrade_info('340202420300715', b'NTB2GV31')
    
    if not upgrade_info:
        logger.error("未找到升级信息")
        return False
    
    logger.info(f"升级信息: {upgrade_info}")
    
    # 检查是否包含文件数据
    if 'file_data' not in upgrade_info or not upgrade_info['file_data']:
        logger.error("升级信息中没有文件数据")
        return False
    
    logger.info(f"文件数据大小: {len(upgrade_info['file_data'])} 字节")
    
    # 3. 创建程序管理器并直接加载文件数据
    logger.info("2. 加载程序数据...")
    program_manager = ProgramManager(grpc_client)
    
    success = program_manager.load_program_from_grpc(
        program_id=upgrade_info['program_id'],
        file_data=upgrade_info['file_data'],
        version=int(upgrade_info['platform_version']),
        area=0,
        option=0,
        mark1=0,
        mark2=0,
        file_url=upgrade_info.get('file_url')
    )
    
    if not success:
        logger.error("加载程序数据失败")
        return False
    
    logger.info("程序数据加载成功")
    
    # 4. 验证程序数据
    logger.info("3. 验证程序数据...")
    program_data = program_manager.get_program(
        upgrade_info['program_id'], 
        int(upgrade_info['platform_version'])
    )
    
    if not program_data:
        logger.error("无法获取程序数据")
        return False
    
    logger.info(f"程序数据验证成功: {len(program_data['data'])} 字节")
    
    logger.info("=== 新升级流程测试成功 ===")
    return True


if __name__ == '__main__':
    success = test_new_upgrade_flow()
    sys.exit(0 if success else 1)
