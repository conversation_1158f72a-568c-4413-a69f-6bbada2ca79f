#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AWS S3 工具类
"""

import boto3
import logging
from typing import Optional
from botocore.exceptions import ClientError, NoCredentialsError

logger = logging.getLogger(__name__)


class AWSS3Util:
    """AWS S3 工具类"""
    
    def __init__(self, region_name: str, bucket_name: str, access_key: str, secret_key: str):
        """
        初始化 AWS S3 客户端
        
        Args:
            region_name: AWS 区域名称
            bucket_name: S3 桶名称
            access_key: AWS 访问密钥
            secret_key: AWS 密钥
        """
        self.region_name = region_name
        self.bucket_name = bucket_name
        self.access_key = access_key
        self.secret_key = secret_key
        
        try:
            self.s3_client = boto3.client(
                's3',
                region_name=self.region_name,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key
            )
            logger.info(f"AWS S3 client initialized for bucket: {self.bucket_name}")
        except Exception as e:
            logger.error(f"Failed to initialize AWS S3 client: {e}")
            raise
    
    def download_to_file(self, file_url: str) -> Optional[bytes]:
        """
        从 S3 下载文件到内存
        
        Args:
            file_url: 文件的完整 URL 或对象键
            
        Returns:
            bytes: 文件的二进制数据，失败时返回 None
        """
        try:
            # 从 URL 中提取对象键
            if file_url.startswith('http'):
                # 从完整 URL 中提取对象键
                if 'ota/' in file_url:
                    object_key = file_url[file_url.index('ota/'):]
                else:
                    logger.error(f"Invalid S3 URL format: {file_url}")
                    return None
            else:
                # 直接使用作为对象键
                object_key = file_url
            
            logger.info(f"Downloading from S3: bucket={self.bucket_name}, key={object_key}")
            
            # 下载对象
            response = self.s3_client.get_object(Bucket=self.bucket_name, Key=object_key)
            
            # 读取数据
            file_data = response['Body'].read()
            
            logger.info(f"Successfully downloaded {len(file_data)} bytes from S3")
            return file_data
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchKey':
                logger.error(f"File not found in S3: {object_key}")
            elif error_code == 'NoSuchBucket':
                logger.error(f"Bucket not found: {self.bucket_name}")
            else:
                logger.error(f"AWS S3 client error: {e}")
            return None
        except NoCredentialsError:
            logger.error("AWS credentials not found")
            return None
        except Exception as e:
            logger.error(f"Error downloading from S3: {e}")
            return None
    
    def check_file_exists(self, file_url: str) -> bool:
        """
        检查 S3 中文件是否存在
        
        Args:
            file_url: 文件的完整 URL 或对象键
            
        Returns:
            bool: 文件是否存在
        """
        try:
            # 从 URL 中提取对象键
            if file_url.startswith('http'):
                if 'ota/' in file_url:
                    object_key = file_url[file_url.index('ota/'):]
                else:
                    return False
            else:
                object_key = file_url
            
            self.s3_client.head_object(Bucket=self.bucket_name, Key=object_key)
            return True
        except ClientError:
            return False
        except Exception as e:
            logger.error(f"Error checking file existence: {e}")
            return False
    
    def get_file_size(self, file_url: str) -> Optional[int]:
        """
        获取 S3 文件大小
        
        Args:
            file_url: 文件的完整 URL 或对象键
            
        Returns:
            int: 文件大小（字节），失败时返回 None
        """
        try:
            # 从 URL 中提取对象键
            if file_url.startswith('http'):
                if 'ota/' in file_url:
                    object_key = file_url[file_url.index('ota/'):]
                else:
                    return None
            else:
                object_key = file_url
            
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=object_key)
            return response['ContentLength']
        except Exception as e:
            logger.error(f"Error getting file size: {e}")
            return None