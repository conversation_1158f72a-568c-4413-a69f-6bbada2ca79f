#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理
"""

import yaml
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class AWSS3Config:
    """AWS S3 配置类"""
    
    def __init__(self, region_name: str, bucket_name: str, access_key: str, secret_key: str, visit_path: str = None):
        self.region_name = region_name
        self.bucket_name = bucket_name
        self.access_key = access_key
        self.secret_key = secret_key
        self.visit_path = visit_path
    
    @classmethod
    def from_file(cls, config_file: str) -> 'AWSS3Config':
        """从配置文件加载 AWS S3 配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            return cls(
                region_name=config_data.get('regionName'),
                bucket_name=config_data.get('bucketName'),
                access_key=config_data.get('accessKey'),
                secret_key=config_data.get('secretKey'),
                visit_path=config_data.get('visitPath')
            )
        except Exception as e:
            logger.error(f"Failed to load AWS S3 config from {config_file}: {e}")
            raise
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'AWSS3Config':
        """从字典创建 AWS S3 配置"""
        return cls(
            region_name=config_dict.get('regionName'),
            bucket_name=config_dict.get('bucketName'),
            access_key=config_dict.get('accessKey'),
            secret_key=config_dict.get('secretKey'),
            visit_path=config_dict.get('visitPath')
        )


class OTAUpgrade:
    """OTA 升级信息类"""
    
    def __init__(self, vin: str, upgrade_type: int, platform_version: str, after_change_url: str):
        self.vin = vin
        self.type = upgrade_type  # 0: CCU, 1: BMS
        self.platform_version = platform_version
        self.after_change_url = after_change_url