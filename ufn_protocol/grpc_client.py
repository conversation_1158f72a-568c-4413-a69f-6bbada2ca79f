#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
gRPC客户端，用于查询平台端升级包信息
"""

import os
import sys
import logging
import grpc
from typing import Optional, Dict
from .constants import OTASID

logger = logging.getLogger(__name__)

# 添加grpc_generated到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'grpc_gene'))

try:
    import ota_service_pb2
    import ota_service_pb2_grpc
except ImportError as e:
    logger.error(f"Failed to import gRPC modules: {e}")
    ota_service_pb2 = None
    ota_service_pb2_grpc = None


class GRPCClient:
    """gRPC客户端"""

    def __init__(self, grpc_server_url: str):
        self.grpc_server_url = grpc_server_url
        self.channel = None
        self.stub = None
        self._initialize_connection()

    def _initialize_connection(self):
        """初始化gRPC连接"""
        if not ota_service_pb2_grpc:
            logger.error("gRPC modules not available")
            return

        try:
            self.channel = grpc.insecure_channel(self.grpc_server_url)
            self.stub = ota_service_pb2_grpc.OTAServiceStub(self.channel)
            logger.info(f"Initialized gRPC connection to {self.grpc_server_url}")
        except Exception as e:
            logger.error(f"Failed to initialize gRPC connection: {e}")

    def query_upgrade_info(self, device_id: str, program_id: str) -> Optional[Dict]:
        """
        查询升级包信息

        Args:
            device_id: 设备编码
            program_id: 升级程序类型 (1:CCU,2:BMS)

        Returns:
            升级包信息字典，包含：
            - device_id: 设备编码
            - file_url: 文件地址（保留用于兼容性）
            - program_id: 升级程序类型
            - platform_version: 平台版本
            - available: 是否有可用升级
            - file_data: 文件字节数组（直接返回，无需二次请求）
        """
        try:
            if not self.stub or not ota_service_pb2:
                logger.error("gRPC connection not available")
                return None

            # # 将字节形式的program_id转换为字符串
            # if isinstance(program_id, bytes):
            #     program_id_str = program_id.decode('ascii', errors='ignore').strip('\x00')
            # else:
            #     program_id_str = str(program_id)
            # program_id_str_enum = ota_service_pb2.ProgramType.Value(program_id_str)
            logger.info(f"grpc client received program_id: {program_id}")
            
            try:
                otasid_member = OTASID.from_byte_value(program_id)
                if otasid_member is not None:
                    program_enum = ota_service_pb2.ProgramType.Value(otasid_member.name)
                else:
                    logger.warning(f"Unknown program_id: {program_id}")
                    program_enum = ota_service_pb2.ProgramType.UNKNOWN
            except (ValueError, AttributeError) as e:
                logger.warning(f"Failed to convert program_id {program_id}: {e}")
                program_enum = ota_service_pb2.ProgramType.UNKNOWN

            
            logger.info(f"请求设备: {device_id}, 类型为: {ota_service_pb2.ProgramType.Name(program_enum)} 的升级信息")

            # 创建gRPC请求
            request = ota_service_pb2.UpgradeInfoRequest(
                device_id=device_id,
                program_type=program_enum
            )

            # 发送gRPC请求
            response = self.stub.QueryUpgradeInfo(request)

            if response.available:
                result = {
                    'device_id': response.device_id,
                    'file_url': response.file_url,
                    'program_id': OTASID.get_byte_by_value(response.program_type), # 返回二进制
                    'platform_version': response.platform_version,
                    'available': response.available,
                    'file_data': response.file_data
                }
                logger.info(f"Received upgrade info: {result}")
                return result
            else:
                logger.info(f"No upgrade available for device {device_id}, program {ota_service_pb2.ProgramType.Name(response.program_type)}")
                return None
            
        except Exception as e:
            logger.error(f"Failed to query upgrade info: {e}")
            return None
        
    def download_file_content(self, file_url: str) -> Optional[bytes]:
        """
        通过 gRPC 下载文件内容

        @deprecated: 此方法已弃用。现在 query_upgrade_info 直接返回文件字节数组，
                    无需二次请求文件内容。

        Args:
            file_url: 文件地址

        Returns:
            文件的二进制数据，失败时返回 None
        """
        try:
            if not self.stub or not ota_service_pb2:
                logger.error("gRPC connection not available")
                return None

            logger.info(f"Downloading file content via gRPC: {file_url}")

            # 创建gRPC请求
            request = ota_service_pb2.FileDownloadRequest(file_url=file_url)

            # 发送gRPC请求
            response = self.stub.DownloadFileContent(request)

            if response.success:
                logger.info(f"Successfully downloaded {len(response.file_data)} bytes via gRPC")
                return response.file_data
            else:
                logger.error(f"Failed to download file: {response.error_message}")
                return None
            
        except Exception as e:
            logger.error(f"Failed to download file content: {e}")
            return None
    
    def query_file_size(self, file_url: str) -> Optional[int]:
        """
        查询文件大小
        
        Args:
            file_url: 文件地址
            
        Returns:
            文件大小（字节），失败时返回 None
        """
        try:
            if not self.stub or not ota_service_pb2:
                logger.error("gRPC connection not available")
                return None

            # 创建gRPC请求
            request = ota_service_pb2.FileSizeRequest(file_url=file_url)

            # 发送gRPC请求
            response = self.stub.QueryFileSize(request)

            if response.success:
                logger.info(f"File size: {response.file_size} bytes")
                return response.file_size
            else:
                logger.error(f"Failed to query file size: {response.error_message}")
                return None
        except Exception as e:
            logger.error(f"Failed to query file size: {e}")
            return None
