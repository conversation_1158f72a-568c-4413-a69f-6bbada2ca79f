# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ota_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'ota_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11ota_service.proto\x12\x03ota\";\n\x12UpgradeInfoRequest\x12\x11\n\tdevice_id\x18\x01 \x01(\t\x12\x12\n\nprogram_id\x18\x02 \x01(\t\"{\n\x13UpgradeInfoResponse\x12\x11\n\tdevice_id\x18\x01 \x01(\t\x12\x10\n\x08\x66ile_url\x18\x02 \x01(\t\x12\x12\n\nprogram_id\x18\x03 \x01(\t\x12\x18\n\x10platform_version\x18\x04 \x01(\t\x12\x11\n\tavailable\x18\x05 \x01(\x08\"\'\n\x13\x46ileDownloadRequest\x12\x10\n\x08\x66ile_url\x18\x01 \x01(\t\"Q\n\x14\x46ileDownloadResponse\x12\x11\n\tfile_data\x18\x01 \x01(\x0c\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x15\n\rerror_message\x18\x03 \x01(\t\"#\n\x0f\x46ileSizeRequest\x12\x10\n\x08\x66ile_url\x18\x01 \x01(\t\"M\n\x10\x46ileSizeResponse\x12\x11\n\tfile_size\x18\x01 \x01(\x03\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x15\n\rerror_message\x18\x03 \x01(\t2\xdd\x01\n\nOTAService\x12\x45\n\x10QueryUpgradeInfo\x12\x17.ota.UpgradeInfoRequest\x1a\x18.ota.UpgradeInfoResponse\x12J\n\x13\x44ownloadFileContent\x12\x18.ota.FileDownloadRequest\x1a\x19.ota.FileDownloadResponse\x12<\n\rQueryFileSize\x12\x14.ota.FileSizeRequest\x1a\x15.ota.FileSizeResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ota_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_UPGRADEINFOREQUEST']._serialized_start=26
  _globals['_UPGRADEINFOREQUEST']._serialized_end=85
  _globals['_UPGRADEINFORESPONSE']._serialized_start=87
  _globals['_UPGRADEINFORESPONSE']._serialized_end=210
  _globals['_FILEDOWNLOADREQUEST']._serialized_start=212
  _globals['_FILEDOWNLOADREQUEST']._serialized_end=251
  _globals['_FILEDOWNLOADRESPONSE']._serialized_start=253
  _globals['_FILEDOWNLOADRESPONSE']._serialized_end=334
  _globals['_FILESIZEREQUEST']._serialized_start=336
  _globals['_FILESIZEREQUEST']._serialized_end=371
  _globals['_FILESIZERESPONSE']._serialized_start=373
  _globals['_FILESIZERESPONSE']._serialized_end=450
  _globals['_OTASERVICE']._serialized_start=453
  _globals['_OTASERVICE']._serialized_end=674
# @@protoc_insertion_point(module_scope)
