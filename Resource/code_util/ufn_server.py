#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UFN (Upgrade From NET) Protocol Server Framework
远程升级 UFN 协议服务端框架
"""

import socket
import threading
import struct
import time
import logging
from typing import Dict, Optional, Tuple, Any

import os
from enum import Enum


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UpgradeControl(Enum):
    """升级控制枚举"""
    FORBIDDEN = 0xA0      # 服务器禁止此设备升级
    VERSION_CHECK = 0xA1  # 设备发现版本更新了就升级
    FORCE_UPGRADE = 0xA2  # 立即升级，判断版本一致才下载
    FORCE_REPLACE = 0xA3  # 立即升级，不判断版本
    NO_SUITABLE = 0xA4    # 服务器没有适合该版本程序

class FunctionCode(Enum):
    """功能代码枚举"""
    # 获取版本信息
    GET_VERSION_REQ = 0x8301
    GET_VERSION_RESP = 0x8302
    
    # 获取程序信息
    GET_PROGRAM_INFO_REQ = 0x8403
    GET_PROGRAM_INFO_RESP = 0x8404
    
    # 下载程序数据
    DOWNLOAD_DATA_REQ = 0x9101
    DOWNLOAD_DATA_RESP = 0x9102
    
    # 发送启动信息
    SEND_STARTUP_REQ = 0x8303
    SEND_STARTUP_RESP = 0x8303  # 原封不动返回

class UFNServer:
    """UFN协议服务器"""
    
    def __init__(self, host='0.0.0.0', port=8080, timeout=10):
        self.host = host
        self.port = port
        self.timeout = timeout
        self.socket = None
        self.running = False
        
        # 程序数据存储 - 实际应用中应该从数据库或文件系统加载
        self.program_data: Dict[str, Dict] = {}
        
        # 默认程序块大小
        self.default_chunk_size = 1024
        
    def add_program(self, program_id: str, version: int, data: bytes, 
                   area: int = 0, option: int = 0, mark1: int = 0, mark2: int = 0):
        """添加程序数据"""
        self.program_data[program_id] = {
            'version': version,
            'data': data,
            'area': area,
            'option': option,
            'mark1': mark1,
            'mark2': mark2,
            'size': len(data)
        }
        logger.info(f"Added program {program_id}, version {version}, size {len(data)} bytes")
    
    def load_program_from_file(self, program_id: str, file_path: str, version: int,
                              area: int = 0, option: int = 0, mark1: int = 0, mark2: int = 0):
        """从文件加载程序数据"""
        try:
            with open(file_path, 'rb') as f:
                data = f.read()
            self.add_program(program_id, version, data, area, option, mark1, mark2)
            return True
        except Exception as e:
            logger.error(f"Failed to load program from {file_path}: {e}")
            return False
    
    def calculate_checksum(self, data: bytes) -> int:
        """计算检验和（前面所有数据和）"""
        return sum(data) & 0xFFFFFFFF
    
    def parse_get_version_request(self, data: bytes) -> Optional[Dict]:
        """解析获取版本信息请求"""
        if len(data) < 50:  # 0x32 = 50
            return None
        
        try:
            # 解析报文
            length = struct.unpack('<H', data[0:2])[0]  # 小端
            func_code = struct.unpack('>H', data[2:4])[0]  # 大端
            
            if func_code != FunctionCode.GET_VERSION_REQ.value:
                return None
            
            program_id = data[4:12].hex()
            area = data[12]
            option = data[13]
            mark1 = data[14]
            mark2 = data[15]
            chip_id = data[16:28].hex()
            device_version = struct.unpack('<H', data[28:30])[0]
            device_number = data[30:46].decode('ascii', errors='ignore').strip('\x00')
            checksum = struct.unpack('>I', data[46:50])[0]
            
            # 验证检验和
            calc_checksum = self.calculate_checksum(data[0:46])
            if calc_checksum != checksum:
                logger.warning(f"Checksum mismatch: calc={calc_checksum}, recv={checksum}")
                return None
            
            return {
                'program_id': program_id,
                'area': area,
                'option': option,
                'mark1': mark1,
                'mark2': mark2,
                'chip_id': chip_id,
                'device_version': device_version,
                'device_number': device_number
            }
        except Exception as e:
            logger.error(f"Parse get version request error: {e}")
            return None
    
    def create_get_version_response(self, request_data: Dict) -> bytes:
        """创建获取版本信息响应"""
        program_id = request_data['program_id']
        
        # 查找程序数据
        if program_id in self.program_data:
            program = self.program_data[program_id]
            server_version = program['version']
            device_version = request_data['device_version']
            
            # 确定升级控制
            if server_version > device_version:
                upgrade_control = UpgradeControl.VERSION_CHECK.value
            elif server_version == device_version:
                upgrade_control = UpgradeControl.FORCE_UPGRADE.value
            else:
                upgrade_control = UpgradeControl.FORCE_REPLACE.value
        else:
            server_version = 0
            upgrade_control = UpgradeControl.NO_SUITABLE.value
        
        # 构造响应报文
        response = bytearray()
        response.extend(struct.pack('<H', 0x0018))  # 报文长度
        response.extend(struct.pack('>H', FunctionCode.GET_VERSION_RESP.value))  # 功能代码
        response.extend(struct.pack('<H', server_version))  # 服务器程序版本号
        response.extend(bytes.fromhex(program_id))  # 程序ID
        response.extend(struct.pack('B', upgrade_control))  # 升级控制
        response.extend(struct.pack('B', request_data['area']))  # Area
        response.extend(struct.pack('B', request_data['option']))  # Option
        response.extend(struct.pack('B', request_data['mark1']))  # Mark1
        response.extend(struct.pack('B', request_data['mark2']))  # Mark2
        response.extend(struct.pack('B', 0))  # 预留
        
        # 计算并添加检验和
        checksum = self.calculate_checksum(response)
        response.extend(struct.pack('>I', checksum))
        
        return bytes(response)
    
    def parse_get_program_info_request(self, data: bytes) -> bool:
        """解析获取程序信息请求"""
        if len(data) < 8:
            return False
        
        try:
            length = struct.unpack('<H', data[0:2])[0]
            func_code = struct.unpack('>H', data[2:4])[0]
            checksum = struct.unpack('>I', data[4:8])[0]
            
            # 验证检验和
            calc_checksum = self.calculate_checksum(data[0:4])
            return func_code == FunctionCode.GET_PROGRAM_INFO_REQ.value and calc_checksum == checksum
        except Exception as e:
            logger.error(f"Parse get program info request error: {e}")
            return False
    
    def create_get_program_info_response(self, program_id: str) -> bytes:
        """创建获取程序信息响应"""
        # 程序信息格式（64字节）- 这里简化处理
        program_info = f"Program: {program_id}".ljust(64, '\x00')[:64].encode('ascii')
        
        response = bytearray()
        response.extend(struct.pack('<H', 0x0048))  # 报文长度
        response.extend(struct.pack('>H', FunctionCode.GET_PROGRAM_INFO_RESP.value))  # 功能代码
        response.extend(program_info)  # 程序信息
        
        # 计算并添加检验和
        checksum = self.calculate_checksum(response)
        response.extend(struct.pack('>I', checksum))
        
        return bytes(response)
    
    def parse_download_request(self, data: bytes) -> Optional[Dict]:
        """解析下载程序数据请求"""
        if len(data) < 16:
            return None
        
        try:
            length = struct.unpack('<H', data[0:2])[0]
            func_code = struct.unpack('>H', data[2:4])[0]
            data_index = struct.unpack('>I', data[4:8])[0]
            request_length = struct.unpack('<H', data[8:10])[0]
            checksum = struct.unpack('>I', data[12:16])[0]
            
            # 验证检验和
            calc_checksum = self.calculate_checksum(data[0:12])
            if func_code != FunctionCode.DOWNLOAD_DATA_REQ.value or calc_checksum != checksum:
                return None
            
            return {
                'data_index': data_index,
                'request_length': request_length
            }
        except Exception as e:
            logger.error(f"Parse download request error: {e}")
            return None
    
    def create_download_response(self, program_id: str, data_index: int, request_length: int) -> bytes:
        """创建下载程序数据响应"""
        if program_id not in self.program_data:
            return b''
        
        program = self.program_data[program_id]
        program_data = program['data']
        
        # 计算实际下发数据
        start_pos = data_index
        end_pos = min(start_pos + request_length, len(program_data))
        actual_data = program_data[start_pos:end_pos]
        actual_length = len(actual_data)
        
        # 构造响应报文
        response = bytearray()
        response_length = 12 + actual_length
        response.extend(struct.pack('<H', response_length))  # 报文长度
        response.extend(struct.pack('>H', FunctionCode.DOWNLOAD_DATA_RESP.value))  # 功能代码
        response.extend(struct.pack('>I', data_index))  # 程序数据索引
        response.extend(struct.pack('<H', actual_length))  # 下发数据长度
        response.extend(actual_data)  # 程序数据内容
        
        # 计算并添加检验和
        checksum = self.calculate_checksum(response)
        response.extend(struct.pack('>I', checksum))
        
        return bytes(response)
    
    def parse_startup_info(self, data: bytes) -> Optional[Dict]:
        """解析启动信息"""
        if len(data) < 36:
            return None
        
        try:
            length = struct.unpack('<H', data[0:2])[0]
            func_code = struct.unpack('>H', data[2:4])[0]
            
            if func_code != FunctionCode.SEND_STARTUP_REQ.value:
                return None
            
            program_id = data[4:12].hex()
            ccu_version = struct.unpack('<H', data[12:14])[0]
            device_number = data[14:30].decode('ascii', errors='ignore').strip('\x00')
            bms_version = struct.unpack('<H', data[30:32])[0]
            checksum = struct.unpack('>I', data[32:36])[0]
            
            # 验证检验和
            calc_checksum = self.calculate_checksum(data[0:32])
            if calc_checksum != checksum:
                return None
            
            return {
                'program_id': program_id,
                'ccu_version': ccu_version,
                'device_number': device_number,
                'bms_version': bms_version,
                'raw_data': data
            }
        except Exception as e:
            logger.error(f"Parse startup info error: {e}")
            return None
    
    def handle_client(self, client_socket: socket.socket, client_address: Tuple[str, int]):
        """处理客户端连接"""
        logger.info(f"New client connected: {client_address}")
        
        try:
            client_socket.settimeout(self.timeout)
            current_program_id = None
            
            while self.running:
                try:
                    # 读取报文长度
                    length_data = client_socket.recv(2)
                    if not length_data:
                        break
                    
                    length = struct.unpack('<H', length_data)[0]
                    
                    # 读取剩余数据
                    remaining_data = client_socket.recv(length - 2)
                    if not remaining_data:
                        break
                    
                    full_data = length_data + remaining_data
                    
                    # 解析功能代码
                    func_code = struct.unpack('>H', full_data[2:4])[0]
                    
                    if func_code == FunctionCode.GET_VERSION_REQ.value:
                        # 处理获取版本信息请求
                        request_data = self.parse_get_version_request(full_data)
                        if request_data:
                            current_program_id = request_data['program_id']
                            response = self.create_get_version_response(request_data)
                            client_socket.send(response)
                            logger.info(f"Sent version info for program {current_program_id}")
                    
                    elif func_code == FunctionCode.GET_PROGRAM_INFO_REQ.value:
                        # 处理获取程序信息请求
                        if self.parse_get_program_info_request(full_data) and current_program_id:
                            response = self.create_get_program_info_response(current_program_id)
                            client_socket.send(response)
                            logger.info(f"Sent program info for {current_program_id}")
                    
                    elif func_code == FunctionCode.DOWNLOAD_DATA_REQ.value:
                        # 处理下载程序数据请求
                        request_data = self.parse_download_request(full_data)
                        if request_data and current_program_id:
                            response = self.create_download_response(
                                current_program_id,
                                request_data['data_index'],
                                request_data['request_length']
                            )
                            if response:
                                client_socket.send(response)
                                logger.info(f"Sent data chunk at index {request_data['data_index']}")
                    
                    elif func_code == FunctionCode.SEND_STARTUP_REQ.value:
                        # 处理启动信息
                        startup_data = self.parse_startup_info(full_data)
                        if startup_data:
                            # 原封不动返回
                            client_socket.send(startup_data['raw_data'])
                            logger.info(f"Echoed startup info from {startup_data['device_number']}")
                    
                    else:
                        logger.warning(f"Unknown function code: {func_code:04X}")
                
                except socket.timeout:
                    logger.info(f"Client {client_address} timeout")
                    break
                except Exception as e:
                    logger.error(f"Error handling client {client_address}: {e}")
                    break
        
        finally:
            client_socket.close()
            logger.info(f"Client {client_address} disconnected")
    
    def start(self):
        """启动服务器"""
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            self.socket.bind((self.host, self.port))
            self.socket.listen(5)
            self.running = True
            
            logger.info(f"UFN Server started on {self.host}:{self.port}")
            
            while self.running:
                try:
                    client_socket, client_address = self.socket.accept()
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                except Exception as e:
                    if self.running:
                        logger.error(f"Accept error: {e}")
        
        except Exception as e:
            logger.error(f"Server start error: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """停止服务器"""
        self.running = False
        if self.socket:
            self.socket.close()
        logger.info("UFN Server stopped")


def main():
    """主函数示例"""
    server = UFNServer(host='0.0.0.0', port=8080)
    
    # 示例：添加一些测试程序数据
    # 这里使用虚拟数据，实际应用中应从文件或数据库加载
    test_program_data = b'\x00' * 1024  # 1KB的测试数据
    server.add_program(
        program_id='1234567890abcdef',
        version=10100,  # 1.01.00
        data=test_program_data
    )
    
    # 或者从文件加载
    # server.load_program_from_file('1234567890abcdef', 'firmware.bin', 10100)
    
    try:
        server.start()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
        server.stop()


if __name__ == '__main__':
    main()
