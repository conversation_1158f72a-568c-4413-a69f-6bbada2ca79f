package com.gotion.otacenter.utils;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;

public class ByteUtils {
    /**
     * 原始数据转byte数组
     * */
    public static byte[] hexStringToByteArray(String hexString) {
        hexString = hexString.replaceAll(" ", "");
        int len = hexString.length();
        byte[] bytes = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            // 两位一组，表示一个字节,把这样表示的16进制字符串，还原成一个字节
            bytes[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4) + Character
                    .digit(hexString.charAt(i + 1), 16));
        }
        return bytes;
    }

    /**
     * 截取数组
     * */
    public static byte[] subBytes(byte[] src, int begin, int count) {
        byte[] bs = new byte[count];
        for (int i=begin; i<begin+count; i++) {
            bs[i - begin] = src[i];
        }
        return bs;
    }

    /**
     * byte数组转string
     * */
    public static String byteToString(byte[] src) {
        String r = "";
        for (byte s : src) {
            r = r + byteToHex(s)+" ";
        }
        return r;
    }

    /**
     * byte--16进制
     * @param b
     * @return
     */
    public static String byteToHex(byte b){
        char[] hexDigits = new char[2];
        hexDigits[0] = Character.forDigit((b >> 4) & 0xF, 16);
        hexDigits[1] = Character.forDigit((b & 0xF), 16);
        return new String(hexDigits);
    }

    /**
     * byte--10进制
     * @param res
     * @return
     */
    public static int byteToInt(byte res) {
        return res & 0xff;
    }

    /**
     * byte--ascii码
     * @param b
     * @return
     */
    public static String byteToAscii(byte b) {
        StringBuilder sb = new StringBuilder();
        int value = byteToInt(b);
        sb.append((char) value);
        return sb.toString();
    }

    public static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            hexString.append(b+" ");
        }
        return hexString.toString();
    }
    /**
     * ASCII字节数组转为普通字符串（ASCII对应的字符）
     *
     * @return String
     */
    public static String bytesToAscii(byte[] bytes, int offset, int dateLen) {
        if ((bytes == null) || (bytes.length == 0) || (offset < 0) || (dateLen <= 0)) {
            return null;
        }
        if ((offset >= bytes.length) || (bytes.length - offset < dateLen)) {
            return null;
        }

        String asciiStr = null;
        byte[] data = new byte[dateLen];
        System.arraycopy(bytes, offset, data, 0, dateLen);
        try {
            asciiStr = new String(data, "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
        }
        return asciiStr.trim();
    }


    /**
     * 数字字符串转ASCII码字符串
     *
     * @param content 字符串
     * @return ASCII字符串
     */
    public static String stringToAscii(String content) {
        String result = "";
        int max = content.length();
        for (int i = 0; i < max; i++) {
            char c = content.charAt(i);
            String b = Integer.toHexString(c);
            if (b.length()!=2){
                b = "0" + b;
            }
            if (i == content.length() - 1) {
                result = result + b;
            } else {
                result = result + b + " ";
            }
        }
        return result;
    }

    /**
     * 两个字节转为十进制
     * @param data0 十六进制低位
     * @param data1 十六进制高位
     * @return
     */
    public static int byteToInt(byte data0,byte data1){
        int deci = ((data0 & 0xff)|(data1 & 0xff)<<8);
        return deci;
    }

    /**
     * 十进制转为两个字节
     * @return
     */
    public static byte[] int2bytes(int n) {
        int temp1 = 0,temp2=0;
        byte[] hex = new byte[2];
        if(n < 256) {
            hex[1] = (byte) n;
        } else {
            temp1 = n & 0xff;
            hex[1] = (byte)temp1;
            temp2 = n >> 8;
            hex[0] = (byte)temp2;
        }
        return hex;
    }

    /**
     * byte[] to hex string.这里我们可以将byte转换成int，然后利用Integer.toHexString(int)来转换成16进制字符串。
     * @param src byte[] data
     * @return hex string
     */
    public static String bytesToHexString(byte[] src){
        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
            if (i!= src.length-1) {
                stringBuilder.append(" ");
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 校验和
     *
     * @param msg
     *            需要计算校验和的byte数组
     * @param length
     *            校验和位数
     * @return 计算出的校验和数组
     */
    public static byte[] makeChecksum(byte[] msg, int length) {
        long mSum = 0;
        byte[] mByte = new byte[length];

        /** 逐Byte添加位数和 */
        for (byte byteMsg : msg) {
            long mNum = ((long) byteMsg >= 0) ? (long) byteMsg : ((long) byteMsg + 256);
            mSum += mNum;
        }
        /** end of for (byte byteMsg : msg) */

        /** 位数和转化为Byte数组 */
        for (int liv_Count = 0; liv_Count < length; liv_Count++) {
            mByte[length - liv_Count - 1] = (byte) (mSum >> (liv_Count * 8) & 0xff);
        }
        /** end of for (int liv_Count = 0; liv_Count < length; liv_Count++) */

        return mByte;
    }

    /**
     * 小端大端转换
     * @return
     */
    public static byte[] littleToBigEndian(byte[] bs) {
        byte[] bytes = new byte[bs.length];
        for(int i = 0; i < bs.length; i++) {
            bytes[i] = bs[bs.length-1-i];
        }
        return bytes;
    }

    /**
     * 得到十六进制数的静态方法
     * @param decimalNumber 十进制数
     * @return 四位十六进制数字符串
     */
    public static String getHexString(int decimalNumber) {
        //将十进制数转为十六进制数
        String hex = Integer.toHexString(decimalNumber);
        //转为大写
        //hex = hex.toUpperCase();
        //加长到8位字符，用0补齐
        while (hex.length() < 8) {
            hex = "0" + hex;
        }
        byte[] s = hexStringToByteArray(hex);
        s = littleToBigEndian(s);
        return bytesToHexString(s);
    }

    /**
     * 十六进制转二进制
     * */
    public static String hex2bin(String input) {
        StringBuilder sb = new StringBuilder();
        int len = input.length();
        for (int i = 0; i < len; i++){
            //每1个十六进制位转换为4个二进制位
            String temp = input.substring(i, i + 1);
            int tempInt = Integer.parseInt(temp, 16);
            String tempBin = Integer.toBinaryString(tempInt);
            //如果二进制数不足4位，补0
            if (tempBin.length() < 4){
                int num = 4 - tempBin.length();
                for (int j = 0; j < num; j++){
                    sb.append("0");
                }
            }
            sb.append(tempBin);
        }
        return sb.toString();
    }

    /**
     * 二进制转十六进制
     * */
    public static String bin2hex(String input) {
        StringBuilder sb = new StringBuilder();
        int len = input.length();
        for (int i = 0; i < len / 4; i++){
            //每4个二进制位转换为1个十六进制位
            String temp = input.substring(i * 4, (i + 1) * 4);
            int tempInt = Integer.parseInt(temp, 2);
            String tempHex = Integer.toHexString(tempInt).toUpperCase();
            sb.append(tempHex);
        }

        return sb.toString();
    }

    public static boolean equalsArr(byte[] b1,byte[] b2){
        boolean bo=false;
        for (int i = 0; i < b1.length; i++) {
            if (b1[i]==b2[i]){
                bo=true;
                continue;
            }else {
                bo=false;
                break;
            }
        }
        return bo;
    }

}
