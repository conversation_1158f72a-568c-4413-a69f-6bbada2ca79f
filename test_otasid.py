#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 OTASID 枚举
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ufn_protocol.constants import OTASID

def test_otasid():
    """测试 OTASID 枚举"""
    print("=== 测试 OTASID 枚举 ===")
    
    # 测试 CCU
    print(f"测试 CCU:")
    ccu_bytes = b'NTB2GV31'
    print(f"  输入: {ccu_bytes}")
    ccu_member = OTASID.from_byte_value(ccu_bytes)
    print(f"  结果: {ccu_member}")
    print(f"  类型: {type(ccu_member)}")
    if ccu_member is not None:
        print(f"  名称: {ccu_member.name}")
        print(f"  值: {ccu_member.value}")
    
    # 测试 BMS
    print(f"\n测试 BMS:")
    bms_bytes = b'RNBMSV20'
    print(f"  输入: {bms_bytes}")
    bms_member = OTASID.from_byte_value(bms_bytes)
    print(f"  结果: {bms_member}")
    print(f"  类型: {type(bms_member)}")
    if bms_member is not None:
        print(f"  名称: {bms_member.name}")
        print(f"  值: {bms_member.value}")
    
    # 测试未知值
    print(f"\n测试未知值:")
    unknown_bytes = b'UNKNOWN1'
    print(f"  输入: {unknown_bytes}")
    unknown_member = OTASID.from_byte_value(unknown_bytes)
    print(f"  结果: {unknown_member}")
    
    # 测试反向转换
    print(f"\n测试反向转换:")
    print(f"  OTASID.CCU.byte_value: {OTASID.CCU.byte_value}")
    print(f"  OTASID.BMS.byte_value: {OTASID.BMS.byte_value}")
    
    print("\n=== 测试完成 ===")

if __name__ == '__main__':
    test_otasid()
