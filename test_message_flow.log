2025-07-25 15:20:40,050 - INFO - <PERSON>ce Message Test Script
2025-07-25 15:20:40,050 - INFO - ==================================================
2025-07-25 15:20:40,050 - INFO - Starting OTA flow test...
2025-07-25 15:20:40,052 - INFO - Connected to server localhost:8080
2025-07-25 15:20:40,052 - INFO - 
=== Step 1: Send Get Version Request (8301) ===
2025-07-25 15:20:40,052 - INFO - Sending message: 00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 00 00 13 b0 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-25 15:20:40,052 - INFO - Message length: 64 bytes
2025-07-25 15:20:40,052 - INFO - Received response: 00188302271000a4646464640000000308
2025-07-25 15:20:40,052 - INFO - Response length: 17 bytes
2025-07-25 15:20:40,052 - INFO - Response length: 24
2025-07-25 15:20:40,052 - INFO - Function code: 0x8302
2025-07-25 15:20:40,052 - INFO - Platform version: 10000
2025-07-25 15:20:40,052 - INFO - Upgrade type: 0
2025-07-25 15:20:40,052 - INFO - Upgrade control: 0xA4
2025-07-25 15:20:40,052 - INFO - Area: 100, Option: 100
2025-07-25 15:20:40,052 - INFO - Upgrade available: False
2025-07-25 15:20:40,052 - WARNING - No upgrade available or upgrade forbidden
2025-07-25 15:20:40,052 - INFO - Disconnected from server
2025-07-25 15:20:40,052 - ERROR - ✗ Some tests failed!
2025-07-25 15:31:52,277 - INFO - Device Message Test Script
2025-07-25 15:31:52,277 - INFO - ==================================================
2025-07-25 15:31:52,277 - INFO - Starting OTA flow test...
2025-07-25 15:31:52,279 - INFO - Connected to server localhost:8080
2025-07-25 15:31:52,279 - INFO - 
=== Step 1: Send Get Version Request (8301) ===
2025-07-25 15:31:52,279 - INFO - Sending message: 00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 00 00 13 b0 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-25 15:31:52,279 - INFO - Message length: 64 bytes
2025-07-25 15:31:52,279 - INFO - Received response: 00188302271000a4646464640000000308
2025-07-25 15:31:52,279 - INFO - Response length: 17 bytes
2025-07-25 15:31:52,279 - INFO - Response length: 24
2025-07-25 15:31:52,279 - INFO - Function code: 0x8302
2025-07-25 15:31:52,279 - INFO - Platform version: 10000
2025-07-25 15:31:52,279 - INFO - Upgrade type: 0
2025-07-25 15:31:52,279 - INFO - Upgrade control: 0xA4
2025-07-25 15:31:52,279 - INFO - Area: 100, Option: 100
2025-07-25 15:31:52,279 - INFO - Upgrade available: False
2025-07-25 15:31:52,279 - WARNING - No upgrade available or upgrade forbidden
2025-07-25 15:31:52,279 - INFO - Disconnected from server
2025-07-25 15:31:52,279 - ERROR - ✗ Some tests failed!
2025-07-25 15:32:24,960 - INFO - Device Message Test Script
2025-07-25 15:32:24,960 - INFO - ==================================================
2025-07-25 15:32:24,960 - INFO - Starting OTA flow test...
2025-07-25 15:32:24,961 - INFO - Connected to server localhost:8080
2025-07-25 15:32:24,961 - INFO - 
=== Step 1: Send Get Version Request (8301) ===
2025-07-25 15:32:24,961 - INFO - Sending message: 00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 00 00 13 b0 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-25 15:32:24,961 - INFO - Message length: 64 bytes
2025-07-25 15:32:24,962 - INFO - Received response: 00188302271000a4646464640000000308
2025-07-25 15:32:24,962 - INFO - Response length: 17 bytes
2025-07-25 15:32:24,962 - INFO - Response length: 24
2025-07-25 15:32:24,962 - INFO - Function code: 0x8302
2025-07-25 15:32:24,962 - INFO - Platform version: 10000
2025-07-25 15:32:24,962 - INFO - Upgrade type: 0
2025-07-25 15:32:24,962 - INFO - Upgrade control: 0xA4
2025-07-25 15:32:24,962 - INFO - Area: 100, Option: 100
2025-07-25 15:32:24,962 - INFO - Upgrade available: False
2025-07-25 15:32:24,962 - WARNING - No upgrade available or upgrade forbidden
2025-07-25 15:32:24,962 - INFO - Disconnected from server
2025-07-25 15:32:24,962 - ERROR - ✗ Some tests failed!
2025-07-25 15:32:40,187 - INFO - Device Message Test Script
2025-07-25 15:32:40,187 - INFO - ==================================================
2025-07-25 15:32:40,187 - INFO - Starting OTA flow test...
2025-07-25 15:32:40,188 - INFO - Connected to server localhost:8080
2025-07-25 15:32:40,188 - INFO - 
=== Step 1: Send Get Version Request (8301) ===
2025-07-25 15:32:40,188 - INFO - Sending message: 00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 00 00 13 b0 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-25 15:32:40,188 - INFO - Message length: 64 bytes
2025-07-25 15:32:40,189 - INFO - Received response: 00188302271000a4646464640000000308
2025-07-25 15:32:40,189 - INFO - Response length: 17 bytes
2025-07-25 15:32:40,189 - INFO - Response length: 24
2025-07-25 15:32:40,189 - INFO - Function code: 0x8302
2025-07-25 15:32:40,189 - INFO - Platform version: 10000
2025-07-25 15:32:40,189 - INFO - Upgrade type: 0
2025-07-25 15:32:40,189 - INFO - Upgrade control: 0xA4
2025-07-25 15:32:40,189 - INFO - Area: 100, Option: 100
2025-07-25 15:32:40,189 - INFO - Upgrade available: False
2025-07-25 15:32:40,189 - WARNING - No upgrade available or upgrade forbidden
2025-07-25 15:32:40,189 - INFO - Disconnected from server
2025-07-25 15:32:40,189 - ERROR - ✗ Some tests failed!
2025-07-25 15:33:37,020 - INFO - Device Message Test Script
2025-07-25 15:33:37,021 - INFO - ==================================================
2025-07-25 15:33:37,021 - INFO - Starting OTA flow test...
2025-07-25 15:33:37,022 - INFO - Connected to server localhost:8080
2025-07-25 15:33:37,022 - INFO - 
=== Step 1: Send Get Version Request (8301) ===
2025-07-25 15:33:37,022 - INFO - Sending message: 00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 00 00 13 b0 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-25 15:33:37,022 - INFO - Message length: 64 bytes
2025-07-25 15:33:37,022 - INFO - Received response: 00188302271000a4646464640000000308
2025-07-25 15:33:37,022 - INFO - Response length: 17 bytes
2025-07-25 15:33:37,022 - INFO - Response length: 24
2025-07-25 15:33:37,022 - INFO - Function code: 0x8302
2025-07-25 15:33:37,022 - INFO - Platform version: 10000
2025-07-25 15:33:37,022 - INFO - Upgrade type: 0
2025-07-25 15:33:37,022 - INFO - Upgrade control: 0xA4
2025-07-25 15:33:37,022 - INFO - Area: 100, Option: 100
2025-07-25 15:33:37,023 - INFO - Upgrade available: False
2025-07-25 15:33:37,023 - WARNING - No upgrade available or upgrade forbidden
2025-07-25 15:33:37,023 - INFO - Disconnected from server
2025-07-25 15:33:37,023 - ERROR - ✗ Some tests failed!
2025-07-25 15:38:59,187 - INFO - Device Message Test Script
2025-07-25 15:38:59,187 - INFO - ==================================================
2025-07-25 15:38:59,187 - INFO - Starting OTA flow test...
2025-07-25 15:38:59,189 - INFO - Connected to server localhost:8080
2025-07-25 15:38:59,189 - INFO - 
=== Step 1: Send Get Version Request (8301) ===
2025-07-25 15:38:59,189 - INFO - Sending message: 00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 00 00 13 b0 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-25 15:38:59,189 - INFO - Message length: 64 bytes
2025-07-25 15:38:59,189 - INFO - Received response: 00188302271000a4646464640000000308
2025-07-25 15:38:59,189 - INFO - Response length: 17 bytes
2025-07-25 15:38:59,189 - INFO - Response length: 24
2025-07-25 15:38:59,189 - INFO - Function code: 0x8302
2025-07-25 15:38:59,189 - INFO - Platform version: 10000
2025-07-25 15:38:59,189 - INFO - Upgrade type: 0
2025-07-25 15:38:59,189 - INFO - Upgrade control: 0xA4
2025-07-25 15:38:59,189 - INFO - Area: 100, Option: 100
2025-07-25 15:38:59,189 - INFO - Upgrade available: False
2025-07-25 15:38:59,189 - WARNING - No upgrade available or upgrade forbidden
2025-07-25 15:38:59,189 - INFO - Disconnected from server
2025-07-25 15:38:59,189 - ERROR - ✗ Some tests failed!
2025-07-25 16:11:47,222 - INFO - Device Message Test Script
2025-07-25 16:11:47,222 - INFO - ==================================================
2025-07-25 16:11:47,222 - INFO - Starting OTA flow test...
2025-07-25 16:11:47,224 - INFO - Connected to server localhost:8080
2025-07-25 16:11:47,224 - INFO - 
=== Step 1: Send Get Version Request (8301) ===
2025-07-25 16:11:47,224 - INFO - Sending message: 00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 00 00 13 b0 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-25 16:11:47,224 - INFO - Message length: 64 bytes
2025-07-25 16:11:47,227 - INFO - Received response: 00188302271000a4646464640000000308
2025-07-25 16:11:47,227 - INFO - Response length: 17 bytes
2025-07-25 16:11:47,227 - INFO - Response length: 24
2025-07-25 16:11:47,227 - INFO - Function code: 0x8302
2025-07-25 16:11:47,227 - INFO - Platform version: 10000
2025-07-25 16:11:47,227 - INFO - Upgrade type: 0
2025-07-25 16:11:47,227 - INFO - Upgrade control: 0xA4
2025-07-25 16:11:47,227 - INFO - Area: 100, Option: 100
2025-07-25 16:11:47,227 - INFO - Upgrade available: False
2025-07-25 16:11:47,227 - WARNING - No upgrade available or upgrade forbidden
2025-07-25 16:11:47,227 - INFO - Disconnected from server
2025-07-25 16:11:47,227 - ERROR - ✗ Some tests failed!
2025-07-25 16:15:29,561 - INFO - Device Message Test Script
2025-07-25 16:15:29,561 - INFO - ==================================================
2025-07-25 16:15:29,561 - INFO - Starting OTA flow test...
2025-07-25 16:15:29,562 - INFO - Connected to server localhost:8080
2025-07-25 16:15:29,562 - INFO - 
=== Step 1: Send Get Version Request (8301) ===
2025-07-25 16:15:29,563 - INFO - Sending message: 00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 00 00 13 b0 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-25 16:15:29,563 - INFO - Message length: 64 bytes
2025-07-25 16:15:29,565 - INFO - Received response: 00188302271000a4646464640000000308
2025-07-25 16:15:29,565 - INFO - Response length: 17 bytes
2025-07-25 16:15:29,565 - INFO - Response length: 24
2025-07-25 16:15:29,565 - INFO - Function code: 0x8302
2025-07-25 16:15:29,565 - INFO - Platform version: 10000
2025-07-25 16:15:29,565 - INFO - Upgrade type: 0
2025-07-25 16:15:29,565 - INFO - Upgrade control: 0xA4
2025-07-25 16:15:29,565 - INFO - Area: 100, Option: 100
2025-07-25 16:15:29,565 - INFO - Upgrade available: False
2025-07-25 16:15:29,565 - WARNING - No upgrade available or upgrade forbidden
2025-07-25 16:15:29,565 - INFO - Disconnected from server
2025-07-25 16:15:29,565 - ERROR - ✗ Some tests failed!
