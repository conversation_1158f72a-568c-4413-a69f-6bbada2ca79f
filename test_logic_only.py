#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的逻辑（不依赖 gRPC 服务器）
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ufn_protocol.program_manager import ProgramManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_program_manager_logic():
    """测试程序管理器的新逻辑"""
    logger.info("=== 测试程序管理器新逻辑 ===")
    
    # 创建程序管理器（不需要 gRPC 客户端）
    program_manager = ProgramManager()
    
    # 模拟文件数据
    mock_file_data = b"MOCK_UPGRADE_DATA_" * 100  # 1800 字节的模拟数据
    program_id = b'NTB2GV31'
    version = 10400
    
    logger.info(f"模拟文件数据大小: {len(mock_file_data)} 字节")
    
    # 测试新的 load_program_from_grpc 方法
    logger.info("1. 测试加载程序数据...")
    success = program_manager.load_program_from_grpc(
        program_id=program_id,
        file_data=mock_file_data,
        version=version,
        area=1,
        option=2,
        mark1=3,
        mark2=4,
        file_url="mock://test.bin"  # 可选参数
    )
    
    if not success:
        logger.error("加载程序数据失败")
        return False
    
    logger.info("程序数据加载成功")
    
    # 验证程序数据
    logger.info("2. 验证程序数据...")
    program_data = program_manager.get_program(program_id, version)
    
    if not program_data:
        logger.error("无法获取程序数据")
        return False
    
    # 检查数据完整性
    expected_fields = ['version', 'program_id', 'data', 'info', 'size', 'area', 'option', 'mark1', 'mark2']
    for field in expected_fields:
        if field not in program_data:
            logger.error(f"缺少字段: {field}")
            return False
    
    # 验证数据内容
    if program_data['data'] != mock_file_data:
        logger.error("文件数据不匹配")
        return False
    
    if program_data['size'] != len(mock_file_data):
        logger.error("文件大小不匹配")
        return False
    
    if program_data['version'] != version:
        logger.error("版本号不匹配")
        return False
    
    if program_data['program_id'] != program_id:
        logger.error("程序ID不匹配")
        return False
    
    # 验证其他参数
    if program_data['area'] != 1:
        logger.error("area 参数不匹配")
        return False
    
    if program_data['option'] != 2:
        logger.error("option 参数不匹配")
        return False
    
    if program_data['mark1'] != 3:
        logger.error("mark1 参数不匹配")
        return False
    
    if program_data['mark2'] != 4:
        logger.error("mark2 参数不匹配")
        return False
    
    logger.info(f"程序数据验证成功:")
    logger.info(f"  - 版本: {program_data['version']}")
    logger.info(f"  - 程序ID: {program_data['program_id']}")
    logger.info(f"  - 数据大小: {program_data['size']} 字节")
    logger.info(f"  - 区域: {program_data['area']}")
    logger.info(f"  - 选项: {program_data['option']}")
    logger.info(f"  - 标记1: {program_data['mark1']}")
    logger.info(f"  - 标记2: {program_data['mark2']}")
    logger.info(f"  - 程序信息长度: {len(program_data['info'])} 字节")
    
    # 测试获取程序数据的不同方式
    logger.info("3. 测试获取程序数据的不同方式...")
    
    # 通过 program_id 和 version 获取
    program_data_2 = program_manager.get_program(program_id, version)
    if program_data_2 != program_data:
        logger.error("通过 program_id 和 version 获取的数据不一致")
        return False
    
    # 只通过 program_id 获取（应该返回最新版本）
    program_data_3 = program_manager.get_program(program_id)
    if program_data_3 != program_data:
        logger.error("只通过 program_id 获取的数据不一致")
        return False
    
    logger.info("=== 程序管理器新逻辑测试成功 ===")
    return True


if __name__ == '__main__':
    success = test_program_manager_logic()
    sys.exit(0 if success else 1)
