# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ota_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'ota_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11ota_service.proto\x12\x03ota\"O\n\x12UpgradeInfoRequest\x12\x11\n\tdevice_id\x18\x01 \x01(\t\x12&\n\x0cprogram_type\x18\x02 \x01(\x0e\x32\x10.ota.ProgramType\"\xa2\x01\n\x13UpgradeInfoResponse\x12\x11\n\tdevice_id\x18\x01 \x01(\t\x12\x10\n\x08\x66ile_url\x18\x02 \x01(\t\x12&\n\x0cprogram_type\x18\x03 \x01(\x0e\x32\x10.ota.ProgramType\x12\x18\n\x10platform_version\x18\x04 \x01(\t\x12\x11\n\tavailable\x18\x05 \x01(\x08\x12\x11\n\tfile_data\x18\x06 \x01(\x0c*,\n\x0bProgramType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x07\n\x03\x43\x43U\x10\x01\x12\x07\n\x03\x42MS\x10\x02\x32S\n\nOTAService\x12\x45\n\x10QueryUpgradeInfo\x12\x17.ota.UpgradeInfoRequest\x1a\x18.ota.UpgradeInfoResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ota_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_PROGRAMTYPE']._serialized_start=272
  _globals['_PROGRAMTYPE']._serialized_end=316
  _globals['_UPGRADEINFOREQUEST']._serialized_start=26
  _globals['_UPGRADEINFOREQUEST']._serialized_end=105
  _globals['_UPGRADEINFORESPONSE']._serialized_start=108
  _globals['_UPGRADEINFORESPONSE']._serialized_end=270
  _globals['_OTASERVICE']._serialized_start=318
  _globals['_OTASERVICE']._serialized_end=401
# @@protoc_insertion_point(module_scope)
