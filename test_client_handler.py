#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试客户端处理器的新逻辑（模拟 gRPC 响应）
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ufn_protocol.program_manager import ProgramManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockGRPCClient:
    """模拟 gRPC 客户端"""
    
    def query_upgrade_info(self, device_id: str, program_id: bytes):
        """模拟查询升级信息"""
        logger.info(f"Mock gRPC: query_upgrade_info({device_id}, {program_id})")
        
        # 模拟返回数据
        if program_id == b'NTB2GV31':
            mock_file_data = b"MOCK_CCU_UPGRADE_DATA_" * 100  # 2200 字节
            return {
                'device_id': device_id,
                'file_url': 'mock://ccu_upgrade.bin',
                'program_id': program_id,
                'platform_version': '10400',
                'available': True,
                'file_data': mock_file_data
            }
        elif program_id == b'RNBMSV20':
            mock_file_data = b"MOCK_BMS_UPGRADE_DATA_" * 80  # 1760 字节
            return {
                'device_id': device_id,
                'file_url': 'mock://bms_upgrade.bin',
                'program_id': program_id,
                'platform_version': '10200',
                'available': True,
                'file_data': mock_file_data
            }
        else:
            return None


def test_client_handler_logic():
    """测试客户端处理器的新逻辑"""
    logger.info("=== 测试客户端处理器新逻辑 ===")
    
    # 创建模拟的 gRPC 客户端和程序管理器
    mock_grpc_client = MockGRPCClient()
    program_manager = ProgramManager(mock_grpc_client)
    
    # 模拟 _handle_get_version 的逻辑
    logger.info("1. 模拟 _handle_get_version 逻辑...")
    
    device_number = '340202420300715'
    program_id = b'NTB2GV31'
    device_version = 10300  # 设备当前版本
    
    # 查询升级信息
    upgrade_info = mock_grpc_client.query_upgrade_info(device_number, program_id)
    
    if not upgrade_info:
        logger.error("未找到升级信息")
        return False
    
    platform_version = int(upgrade_info['platform_version'])
    logger.info(f"平台版本: {platform_version}, 设备版本: {device_version}")
    
    # 判断是否需要升级
    if platform_version > device_version:
        logger.info("需要升级")
        
        # 构建响应数据（模拟 response_data）
        response_data = {
            'program_id': program_id,
            'platform_version': platform_version,
            'upgrade_type': 0,  # CCU
            'file_url': upgrade_info['file_url'],
            'file_data': upgrade_info['file_data'],
            'upgrade_available': True,
            'area': 1,
            'option': 2,
            'mark1': 3,
            'mark2': 4
        }
        
        logger.info(f"响应数据构建成功，文件数据大小: {len(response_data['file_data'])} 字节")
        
        # 模拟 _handle_get_program_info 的逻辑
        logger.info("2. 模拟 _handle_get_program_info 逻辑...")
        
        # 使用新的 load_program_from_grpc 方法
        success = program_manager.load_program_from_grpc(
            program_id=response_data['program_id'],
            file_data=response_data['file_data'],
            version=response_data['platform_version'],
            area=response_data['area'],
            option=response_data['option'],
            mark1=response_data['mark1'],
            mark2=response_data['mark2'],
            file_url=response_data.get('file_url')
        )
        
        if not success:
            logger.error("加载程序数据失败")
            return False
        
        logger.info("程序数据加载成功")
        
        # 验证程序数据
        logger.info("3. 验证程序数据...")
        program_data = program_manager.get_program(program_id, platform_version)
        
        if not program_data:
            logger.error("无法获取程序数据")
            return False
        
        logger.info(f"程序数据验证成功:")
        logger.info(f"  - 版本: {program_data['version']}")
        logger.info(f"  - 程序ID: {program_data['program_id']}")
        logger.info(f"  - 数据大小: {program_data['size']} 字节")
        logger.info(f"  - 区域: {program_data['area']}")
        logger.info(f"  - 选项: {program_data['option']}")
        logger.info(f"  - 标记1: {program_data['mark1']}")
        logger.info(f"  - 标记2: {program_data['mark2']}")
        
        logger.info("=== 客户端处理器新逻辑测试成功 ===")
        return True
    else:
        logger.info("不需要升级")
        return True


if __name__ == '__main__':
    success = test_client_handler_logic()
    sys.exit(0 if success else 1)
